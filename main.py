import pandas as pd
import numpy as np

try:
    import spotfire
    platform = 'spotfire'
except:
    platform = 'ide'

if platform == 'ide':
    dv_data = pd.read_excel('data/DV_DATA.xlsx')
    tp_data = pd.read_excel('data/All TP Data.xlsx')

# DV去重
# 分组依据为'PRODUCT_ID', 'STEP_ID', 'GLASS_ID', 'PARAM_NAME'
# 保留GLASS_START_TIME最迟的记录
dv_data_dedup = dv_data.sort_values('GLASS_START_TIME').groupby(
    ['PRODUCT_ID', 'STEP_ID', 'GLASS_ID', 'PARAM_NAME']
).last().reset_index()


# DV处理/转置
# 以PRODUCT_ID、GLASS_ID作为索引，{STEP_ID}_{PARAM_NAME}、{STEP_ID}_{GLASS_START_TIME}作为列
# PARAM_NAME值为DISPENSE_SPEED时，值取PARAM_VALUE列；为PR_ID时，值取STR_VALUE

# 创建值列：根据PARAM_NAME选择对应的值
dv_data_dedup['VALUE'] = dv_data_dedup.apply(
    lambda row: row['PARAM_VALUE'] if row['PARAM_NAME'] == 'DISPENSE_SPEED' else row['STR_VALUE'],
    axis=1
)

# 创建新的列名：{STEP_ID}_{PARAM_NAME}
dv_data_dedup['COLUMN_NAME'] = dv_data_dedup['STEP_ID'].astype(str) + '_' + dv_data_dedup['PARAM_NAME']

# 准备数据透视的数据 - 包含参数值
pivot_data = dv_data_dedup[['PRODUCT_ID', 'GLASS_ID', 'COLUMN_NAME', 'VALUE']].copy()

# 准备时间数据 - 创建{STEP_ID}_{GLASS_START_TIME}格式
time_data = dv_data_dedup[['PRODUCT_ID', 'GLASS_ID', 'STEP_ID', 'GLASS_START_TIME']].copy()
time_data['TIME_COLUMN_NAME'] = time_data['STEP_ID'].astype(str) + '_GLASS_START_TIME'
time_data = time_data[['PRODUCT_ID', 'GLASS_ID', 'TIME_COLUMN_NAME', 'GLASS_START_TIME']].drop_duplicates()

# 数据透视：参数值
dv_pivot = pivot_data.pivot_table(
    index=['PRODUCT_ID', 'GLASS_ID'],
    columns='COLUMN_NAME',
    values='VALUE',
    aggfunc='first'  # 如果有重复，取第一个值
).reset_index()

# 数据透视：时间值
time_pivot = time_data.pivot_table(
    index=['PRODUCT_ID', 'GLASS_ID'],
    columns='TIME_COLUMN_NAME',
    values='GLASS_START_TIME',
    aggfunc='first'  # 如果有重复，取第一个值
).reset_index()

# 合并参数数据和时间数据
dv_final = dv_pivot.merge(time_pivot, on=['PRODUCT_ID', 'GLASS_ID'], how='left')

# 输出DV处理结果
if platform == 'ide':
    print("DV数据处理完成！")
    print(f"原始数据形状: {dv_data.shape}")
    print(f"去重后数据形状: {dv_data_dedup.shape}")
    print(f"转置后数据形状: {dv_final.shape}")
    print(f"转置后列名: {dv_final.columns.tolist()}")
    print("\n转置后数据预览:")
    print(dv_final.head())

# TP处理
# 同一RECORD_INDEX为同一组数据，PRODUCT_ID、GLASS_ID、GLASS_END_TIME相同
# 用PRODUCT_ID、GLASS_ID作为索引，查找DV数据
# 找到GLASS_END_TIME恰好在哪个STEP_ID的GLASS_START_TIME之后，为TP数据填充MAIN_PROCESS_STEP列

# TP预处理：移除包含非OK判定的RECORD_INDEX组
print("\n" + "="*50)
print("TP数据预处理开始...")
print(f"原始TP数据形状: {tp_data.shape}")
print(f"原始RECORD_INDEX数量: {tp_data['RECORD_INDEX'].nunique()}")

# 找出包含非OK判定的RECORD_INDEX
error_record_indices = tp_data[tp_data['JUDGE'] != 'OK']['RECORD_INDEX'].unique()
print(f"包含非OK判定的RECORD_INDEX数量: {len(error_record_indices)}")

# 移除这些RECORD_INDEX组
tp_data_filtered = tp_data[~tp_data['RECORD_INDEX'].isin(error_record_indices)].copy()
print(f"过滤后TP数据形状: {tp_data_filtered.shape}")
print(f"过滤后RECORD_INDEX数量: {tp_data_filtered['RECORD_INDEX'].nunique()}")

# 创建DV时间参考表：每个GLASS_ID对应的各STEP_ID的GLASS_START_TIME
dv_time_ref = dv_data_dedup.groupby(['PRODUCT_ID', 'GLASS_ID', 'STEP_ID'])['GLASS_START_TIME'].max().reset_index()
dv_time_ref = dv_time_ref.sort_values(['PRODUCT_ID', 'GLASS_ID', 'STEP_ID'])

# 为TP数据添加MAIN_PROCESS_STEP列
def find_main_process_step(row, dv_time_ref):
    """
    根据TP的GLASS_END_TIME找到对应的DV STEP_ID
    逻辑：找到GLASS_END_TIME恰好在哪个STEP_ID的GLASS_START_TIME之后
    """
    product_id = row['PRODUCT_ID']
    glass_id = row['GLASS_ID']
    glass_end_time = row['GLASS_END_TIME']

    # 找到该GLASS_ID在DV中的所有STEP_ID时间
    glass_steps = dv_time_ref[
        (dv_time_ref['PRODUCT_ID'] == product_id) &
        (dv_time_ref['GLASS_ID'] == glass_id)
    ].sort_values('STEP_ID')

    if len(glass_steps) == 0:
        return None

    # 找到GLASS_END_TIME之前最近的STEP_ID
    valid_steps = glass_steps[glass_steps['GLASS_START_TIME'] <= glass_end_time]

    if len(valid_steps) == 0:
        return None

    # 返回最后一个（最新的）STEP_ID
    return valid_steps.iloc[-1]['STEP_ID']

# 应用函数为TP数据添加MAIN_PROCESS_STEP列
tp_data['MAIN_PROCESS_STEP'] = tp_data.apply(
    lambda row: find_main_process_step(row, dv_time_ref), axis=1
)



# 输出TP处理结果
if platform == 'ide':
    print("\n" + "="*50)
    print("TP数据处理完成！")
    print(f"TP数据形状: {tp_data.shape}")
    print(f"成功匹配MAIN_PROCESS_STEP的记录数: {tp_data['MAIN_PROCESS_STEP'].notna().sum()}")
    print(f"未匹配的记录数: {tp_data['MAIN_PROCESS_STEP'].isna().sum()}")

    # 显示MAIN_PROCESS_STEP的分布
    print("\nMAIN_PROCESS_STEP分布:")
    step_counts = tp_data['MAIN_PROCESS_STEP'].value_counts().sort_index()
    print(step_counts)

    # 显示一些示例匹配结果
    print("\n匹配示例:")
    sample_tp = tp_data[tp_data['MAIN_PROCESS_STEP'].notna()].head(3)
    for idx, row in sample_tp.iterrows():
        print(f"GLASS_ID: {row['GLASS_ID']}, GLASS_END_TIME: {row['GLASS_END_TIME']}, MAIN_PROCESS_STEP: {row['MAIN_PROCESS_STEP']}")

    # 保存处理后的数据
    dv_final.to_excel('data/DV_DATA_processed.xlsx', index=False)
    tp_data.to_excel('data/TP_DATA_processed.xlsx', index=False)
    print(f"\n数据已保存:")
    print(f"- DV处理后数据: data/DV_DATA_processed.xlsx")
    print(f"- TP处理后数据: data/TP_DATA_processed.xlsx")
