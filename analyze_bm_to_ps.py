#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析COA数据：从BM（缩放前）到PS（缩放后）的变换
计算Xppm和Yppm参数
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import time
import re  # 添加正则表达式模块
from compute_ppm import CoordinateTransformSolver, plot_results
from compute_ppm_ds import compute_ppm_affine

def load_bm_ps_data(excel_file):
    """
    从Excel文件加载BM和PS数据
    动态识别芯片ID，并只处理同时在BM和PS表中出现的ID
    """
    # 读取BM sheet（缩放前）
    df_bm = pd.read_excel(excel_file, sheet_name='BM')
    print("BM (缩放前) 数据:")
    print(df_bm.head())
    
    # 读取PS sheet（缩放后）
    df_ps = pd.read_excel(excel_file, sheet_name='PS')
    print("\nPS (缩放后) 数据:")
    print(df_ps.head())
    
    # 获取列名作为可能的ID
    bm_cols = df_bm.columns.tolist()
    ps_cols = df_ps.columns.tolist()
    
    # 识别真正的芯片ID（通常以特定格式开头，如N214049T）
    chip_id_pattern = r'N\d+T\d+'  # 匹配类似N214049T00104的ID格式
    
    bm_ids = []
    for col in bm_cols:
        if isinstance(col, str) and re.match(chip_id_pattern, col):
            bm_ids.append(col)
    
    ps_ids = []
    for col in ps_cols:
        if isinstance(col, str) and re.match(chip_id_pattern, col):
            ps_ids.append(col)
    
    # 找出同时在BM和PS中出现的ID
    common_ids = list(set(bm_ids) & set(ps_ids))
    common_ids.sort()  # 排序以保持一致的顺序
    
    print(f"\n找到的共同芯片ID: {common_ids}")
    
    # 存储每个ID的坐标
    coords_bm = []
    coords_ps = []
    valid_ids = []
    
    for chip_id in common_ids:
        # 在BM中找到ID对应的列索引
        bm_x_col = bm_cols.index(chip_id)
        bm_y_col = bm_x_col + 1  # Y列通常紧跟在X列之后
        
        # 在PS中找到ID对应的列索引
        ps_x_col = ps_cols.index(chip_id)
        ps_y_col = ps_x_col + 1  # Y列通常紧跟在X列之后
        
        # 确保Y列存在且名称合理
        if bm_y_col < len(bm_cols) and ps_y_col < len(ps_cols):
            # 提取BM坐标，跳过第一行（标题行）
            bm_coords = df_bm.iloc[1:, [bm_x_col, bm_y_col]].dropna().values.astype(float)
            
            # 提取PS坐标，跳过第一行（标题行）
            ps_coords = df_ps.iloc[1:, [ps_x_col, ps_y_col]].dropna().values.astype(float)
            
            # 只有当坐标点数大于0时才添加
            if len(bm_coords) > 0 and len(ps_coords) > 0:
                coords_bm.append(bm_coords)
                coords_ps.append(ps_coords)
                valid_ids.append(chip_id)
                print(f"{chip_id}: BM {len(bm_coords)} 点, PS {len(ps_coords)} 点")
            else:
                print(f"警告: {chip_id} 没有有效的坐标点")
        else:
            print(f"警告: {chip_id} 在BM或PS中没有找到完整的X,Y列")
    
    print(f"\n数据统计:")
    for i, chip_id in enumerate(valid_ids):
        print(f"{chip_id}: BM {len(coords_bm[i])} 点, PS {len(coords_ps[i])} 点")
    
    return {
        'BM': coords_bm,
        'PS': coords_ps,
        'IDs': valid_ids
    }

def analyze_bm_to_ps_transform(coords_bm, coords_ps, chip_id):
    """
    分析从BM到PS的变换参数
    """
    print(f"\n{'='*70}")
    print(f"分析芯片: {chip_id} (BM → PS)")
    print(f"{'='*70}")
    
    if len(coords_bm) != len(coords_ps):
        min_len = min(len(coords_bm), len(coords_ps))
        coords_bm = coords_bm[:min_len]
        coords_ps = coords_ps[:min_len]
        print(f"警告: 坐标点数量不匹配，使用前{min_len}个点")
    
    print(f"使用 {len(coords_bm)} 个坐标点对")
    
    # 基本统计
    print(f"\nBM (缩放前) 统计:")
    print(f"  X范围: [{coords_bm[:, 0].min():.2f}, {coords_bm[:, 0].max():.2f}]")
    print(f"  Y范围: [{coords_bm[:, 1].min():.2f}, {coords_bm[:, 1].max():.2f}]")
    print(f"  质心: ({coords_bm[:, 0].mean():.2f}, {coords_bm[:, 1].mean():.2f})")
    
    print(f"\nPS (缩放后) 统计:")
    print(f"  X范围: [{coords_ps[:, 0].min():.2f}, {coords_ps[:, 0].max():.2f}]")
    print(f"  Y范围: [{coords_ps[:, 1].min():.2f}, {coords_ps[:, 1].max():.2f}]")
    print(f"  质心: ({coords_ps[:, 0].mean():.2f}, {coords_ps[:, 1].mean():.2f})")
    
    # 计算差异
    diff = coords_ps - coords_bm
    print(f"\n坐标差异分析:")
    print(f"  X差异: 平均={diff[:, 0].mean():.2e}, 标准差={diff[:, 0].std():.2e}")
    print(f"  Y差异: 平均={diff[:, 1].mean():.2e}, 标准差={diff[:, 1].std():.2e}")
    print(f"  最大X差异: {diff[:, 0].max():.2e}, 最小X差异: {diff[:, 0].min():.2e}")
    print(f"  最大Y差异: {diff[:, 1].max():.2e}, 最小Y差异: {diff[:, 1].min():.2e}")
    
    # 估算伸缩比例
    # 使用标准差比例作为初步估算
    scale_x_std = coords_ps[:, 0].std() / coords_bm[:, 0].std() if coords_bm[:, 0].std() > 0 else 1.0
    scale_y_std = coords_ps[:, 1].std() / coords_bm[:, 1].std() if coords_bm[:, 1].std() > 0 else 1.0
    
    # 使用范围比例作为另一种估算
    range_bm_x = coords_bm[:, 0].max() - coords_bm[:, 0].min()
    range_ps_x = coords_ps[:, 0].max() - coords_ps[:, 0].min()
    range_bm_y = coords_bm[:, 1].max() - coords_bm[:, 1].min()
    range_ps_y = coords_ps[:, 1].max() - coords_ps[:, 1].min()
    
    scale_x_range = range_ps_x / range_bm_x if range_bm_x > 0 else 1.0
    scale_y_range = range_ps_y / range_bm_y if range_bm_y > 0 else 1.0
    
    print(f"\n初步伸缩估算:")
    print(f"  X方向 (标准差法): {scale_x_std:.8f} (ppm: {(scale_x_std-1)*1e6:.1f})")
    print(f"  Y方向 (标准差法): {scale_y_std:.8f} (ppm: {(scale_y_std-1)*1e6:.1f})")
    print(f"  X方向 (范围法): {scale_x_range:.8f} (ppm: {(scale_x_range-1)*1e6:.1f})")
    print(f"  Y方向 (范围法): {scale_y_range:.8f} (ppm: {(scale_y_range-1)*1e6:.1f})")
    
    # 方法1: 使用仿射变换分解算法 (compute_ppm_ds)
    print(f"\n=== 方法1: 仿射变换分解算法 ===")
    try:
        # 测量仿射变换算法的执行时间
        start_time_affine = time.time()
        Xppm_affine, Yppm_affine, theta_affine = compute_ppm_affine(coords_bm, coords_ps)
        affine_time = time.time() - start_time_affine
        
        print(f"仿射变换结果:")
        print(f"  Xppm: {Xppm_affine:.2f} ppm")
        print(f"  Yppm: {Yppm_affine:.2f} ppm")
        print(f"  旋转: {np.degrees(theta_affine):.6f}° ({theta_affine:.2e} rad)")
        print(f"  执行时间: {affine_time*1000:.2f} 毫秒")
        affine_success = True
    except Exception as e:
        print(f"仿射变换算法失败: {e}")
        Xppm_affine = Yppm_affine = theta_affine = affine_time = None
        affine_success = False

    # 方法2: 使用非线性最小二乘法求解器 (compute_ppm)
    print(f"\n=== 方法2: 非线性最小二乘法 ===")
    solver = CoordinateTransformSolver()
    
    if len(coords_bm) < 3:
        print("警告: 点数少于3个，无法求解所有参数")
        return None
    
    print(f"正在求解精确变换参数...")
    # 测量非线性最小二乘法的执行时间
    start_time_nonlinear = time.time()
    success, results = solver.solve(coords_bm, coords_ps)
    nonlinear_time = time.time() - start_time_nonlinear
    
    if success:
        print(f"\n[SUCCESS] 非线性最小二乘法求解成功！")
        print(f"非线性最小二乘法结果:")
        print(f"  Xppm: {results['Xppm']:.2e} ({results['Xppm']*1e6:.2f} ppm)")
        print(f"  Yppm: {results['Yppm']:.2e} ({results['Yppm']*1e6:.2f} ppm)")
        print(f"  旋转: {results['rotation_deg']:.6f}° ({results['rotation_rad']:.2e} rad)")
        print(f"  平移X: {results['translation_x']:.6f}")
        print(f"  平移Y: {results['translation_y']:.6f}")
        print(f"  RMS误差: {results['rms_error']:.2e}")
        print(f"  最大误差: {results['max_error']:.2e}")
        print(f"  迭代次数: {results['iterations']}")
        print(f"  执行时间: {nonlinear_time*1000:.2f} 毫秒")

        # 验证解的准确性
        is_valid, errors = solver.validate_solution(coords_bm, coords_ps, tolerance=0.01)
        print(f"  解的有效性 (容差0.01): {is_valid}")
        print(f"  平均点误差: {np.mean(errors):.2e}")
        print(f"  最大点误差: {np.max(errors):.2e}")

        # 显示详细的点误差分布
        print(f"\n点误差分布:")
        print(f"  < 0.001: {np.sum(errors < 0.001)} 点")
        print(f"  < 0.01:  {np.sum(errors < 0.01)} 点")
        print(f"  < 0.1:   {np.sum(errors < 0.1)} 点")
        print(f"  >= 0.1:  {np.sum(errors >= 0.1)} 点")

        # 算法对比
        if affine_success:
            print(f"\n{'='*50}")
            print("两种算法对比:")
            print(f"{'='*50}")
            print(f"{'参数':<15} {'仿射变换':<15} {'非线性最小二乘':<15} {'差异':<15}")
            print(f"{'-'*60}")
            
            xppm_diff = Xppm_affine - results['Xppm']*1e6
            yppm_diff = Yppm_affine - results['Yppm']*1e6
            theta_diff = np.degrees(theta_affine) - results['rotation_deg']
            time_ratio = nonlinear_time / affine_time if affine_time > 0 else float('inf')
            
            print(f"{'Xppm (ppm)':<15} {Xppm_affine:<15.2f} {results['Xppm']*1e6:<15.2f} {xppm_diff:<15.2f}")
            print(f"{'Yppm (ppm)':<15} {Yppm_affine:<15.2f} {results['Yppm']*1e6:<15.2f} {yppm_diff:<15.2f}")
            print(f"{'旋转 (度)':<15} {np.degrees(theta_affine):<15.6f} {results['rotation_deg']:<15.6f} {theta_diff:<15.6f}")
            print(f"{'执行时间 (ms)':<15} {affine_time*1000:<15.2f} {nonlinear_time*1000:<15.2f} {time_ratio:<15.2f}倍")
            
            print(f"\n算法一致性评估:")
            print(f"  Xppm差异: {abs(xppm_diff):.3f} ppm ({'一致' if abs(xppm_diff) < 0.1 else '存在差异'})")
            print(f"  Yppm差异: {abs(yppm_diff):.3f} ppm ({'一致' if abs(yppm_diff) < 0.1 else '存在差异'})")
            print(f"  旋转差异: {abs(theta_diff):.6f}° ({'一致' if abs(theta_diff) < 0.001 else '存在差异'})")
            print(f"  性能比较: 仿射变换算法比非线性最小二乘法快 {time_ratio:.1f} 倍")

        # 扩展结果字典包含两种算法的结果
        extended_results = results.copy()
        if affine_success:
            extended_results.update({
                'affine_Xppm': Xppm_affine / 1e6,  # 转换为与非线性方法相同的单位
                'affine_Yppm': Yppm_affine / 1e6,
                'affine_rotation_rad': theta_affine,
                'affine_rotation_deg': np.degrees(theta_affine),
                'affine_success': True,
                'xppm_diff': xppm_diff,
                'yppm_diff': yppm_diff,
                'theta_diff': theta_diff,
                'affine_time': affine_time,
                'nonlinear_time': nonlinear_time,
                'time_ratio': time_ratio
            })
        else:
            extended_results.update({
                'affine_success': False,
                'nonlinear_time': nonlinear_time
            })

        return extended_results
    else:
        print(f"\n[FAILED] 非线性最小二乘法求解失败: {results['message']}")

        # 如果非线性方法失败，但仿射变换成功，仍然返回仿射变换结果
        if affine_success:
            print(f"但仿射变换算法成功，返回仿射变换结果")
            return {
                'success': False,
                'Xppm': Xppm_affine / 1e6,
                'Yppm': Yppm_affine / 1e6,
                'rotation_rad': theta_affine,
                'rotation_deg': np.degrees(theta_affine),
                'translation_x': 0,  # 仿射变换不直接给出平移
                'translation_y': 0,
                'rms_error': float('nan'),
                'max_error': float('nan'),
                'affine_success': True,
                'affine_Xppm': Xppm_affine / 1e6,
                'affine_Yppm': Yppm_affine / 1e6,
                'affine_rotation_rad': theta_affine,
                'affine_rotation_deg': np.degrees(theta_affine)
            }

        return None

def main():
    import argparse
    
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='分析BM到PS的变换参数')
    parser.add_argument('--excel', type=str, default="14 TP.xlsx", 
                        help='Excel文件路径 (默认: "14 TP.xlsx")')
    
    args = parser.parse_args()
    excel_file = args.excel
    
    print(f"正在分析Excel文件: {excel_file}")
    
    # 检查文件是否存在
    import os
    if not os.path.exists(excel_file):
        print(f"错误: 文件 '{excel_file}' 不存在")
        return
    
    # 加载BM和PS数据
    data = load_bm_ps_data(excel_file)
    
    # 检查是否找到有效的ID
    if not data['IDs']:
        print("错误: 没有找到有效的芯片ID，请检查Excel文件格式")
        return
    
    results_summary = []
    
    # 分析每个芯片的BM到PS变换
    for i, chip_id in enumerate(data['IDs']):
        coords_bm = data['BM'][i]
        coords_ps = data['PS'][i]
        
        result = analyze_bm_to_ps_transform(coords_bm, coords_ps, chip_id)
        
        if result:
            summary_entry = {
                'chip_id': chip_id,
                'Xppm': result['Xppm'],
                'Yppm': result['Yppm'],
                'Xppm_value': result['Xppm'] * 1e6,  # 转换为ppm单位
                'Yppm_value': result['Yppm'] * 1e6,  # 转换为ppm单位
                'rotation_deg': result['rotation_deg'],
                'translation_x': result['translation_x'],
                'translation_y': result['translation_y'],
                'rms_error': result['rms_error'],
                'max_error': result['max_error']
            }
            
            # 添加仿射变换结果（如果可用）
            if result.get('affine_success', False):
                summary_entry.update({
                    'affine_Xppm_value': result['affine_Xppm'] * 1e6,
                    'affine_Yppm_value': result['affine_Yppm'] * 1e6,
                    'affine_rotation_deg': result['affine_rotation_deg'],
                    'xppm_diff': result.get('xppm_diff', 0),
                    'yppm_diff': result.get('yppm_diff', 0),
                    'theta_diff': result.get('theta_diff', 0),
                    'affine_time': result.get('affine_time', 0),
                    'nonlinear_time': result.get('nonlinear_time', 0),
                    'time_ratio': result.get('time_ratio', 0)
                })
            else:
                summary_entry.update({
                    'affine_Xppm_value': float('nan'),
                    'affine_Yppm_value': float('nan'),
                    'affine_rotation_deg': float('nan'),
                    'xppm_diff': float('nan'),
                    'yppm_diff': float('nan'),
                    'theta_diff': float('nan'),
                    'affine_time': float('nan'),
                    'nonlinear_time': result.get('nonlinear_time', 0),
                    'time_ratio': float('nan')
                })
            
            results_summary.append(summary_entry)
    
    # 汇总结果
    if results_summary:
        print(f"\n{'='*80}")
        print("BM → PS 变换参数汇总")
        print(f"{'='*80}")
        
        df_summary = pd.DataFrame(results_summary)
        
        # 显示主要结果
        print("\n主要结果 (PPM值) - 非线性最小二乘法:")
        for _, row in df_summary.iterrows():
            print(f"{row['chip_id']:15s}: Xppm = {row['Xppm_value']:8.2f} ppm, Yppm = {row['Yppm_value']:8.2f} ppm")

        # 显示仿射变换结果
        print("\n主要结果 (PPM值) - 仿射变换分解法:")
        for _, row in df_summary.iterrows():
            if not np.isnan(row.get('affine_Xppm_value', float('nan'))):
                print(f"{row['chip_id']:15s}: Xppm = {row['affine_Xppm_value']:8.2f} ppm, Yppm = {row['affine_Yppm_value']:8.2f} ppm")
            else:
                print(f"{row['chip_id']:15s}: 仿射变换算法失败")

        print(f"\n详细参数对比表:")
        comparison_cols = ['chip_id', 'Xppm_value', 'affine_Xppm_value', 'xppm_diff',
                          'Yppm_value', 'affine_Yppm_value', 'yppm_diff', 
                          'affine_time', 'nonlinear_time', 'time_ratio', 'rms_error']
        available_cols = [col for col in comparison_cols if col in df_summary.columns]
        
        # 格式化时间列（如果存在）
        if 'affine_time' in df_summary.columns and 'nonlinear_time' in df_summary.columns:
            df_summary['affine_time'] = df_summary['affine_time'] * 1000  # 转换为毫秒
            df_summary['nonlinear_time'] = df_summary['nonlinear_time'] * 1000  # 转换为毫秒
            
        print(df_summary[available_cols].to_string(index=False, float_format='%.3f'))
        
        # 统计分析
        print(f"\nXppm统计 (ppm):")
        print(f"  平均值: {df_summary['Xppm_value'].mean():.2f} ppm")
        print(f"  标准差: {df_summary['Xppm_value'].std():.2f} ppm")
        print(f"  范围: [{df_summary['Xppm_value'].min():.2f}, {df_summary['Xppm_value'].max():.2f}] ppm")
        
        print(f"\nYppm统计 (ppm):")
        print(f"  平均值: {df_summary['Yppm_value'].mean():.2f} ppm")
        print(f"  标准差: {df_summary['Yppm_value'].std():.2f} ppm")
        print(f"  范围: [{df_summary['Yppm_value'].min():.2f}, {df_summary['Yppm_value'].max():.2f}] ppm")
        
        # 性能统计
        if 'affine_time' in df_summary.columns and 'nonlinear_time' in df_summary.columns:
            print(f"\n算法性能对比:")
            print(f"  仿射变换算法平均执行时间: {df_summary['affine_time'].mean():.2f} 毫秒")
            print(f"  非线性最小二乘法平均执行时间: {df_summary['nonlinear_time'].mean():.2f} 毫秒")
            print(f"  平均速度比: 仿射变换算法比非线性最小二乘法快 {df_summary['time_ratio'].mean():.1f} 倍")
        
        # 保存结果
        output_file = "BM_to_PS_analysis_results.xlsx"
        df_summary.to_excel(output_file, index=False)
        print(f"\n结果已保存到: {output_file}")
        
        # 生成可视化图表
        # create_visualization(data, results_summary)

def create_visualization(data, results_summary):
    """
    创建可视化图表
    """
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('BM → PS 变换分析结果', fontsize=16)
    
    # PPM值柱状图
    chip_ids = [r['chip_id'] for r in results_summary]
    xppm_values = [r['Xppm_value'] for r in results_summary]
    yppm_values = [r['Yppm_value'] for r in results_summary]
    
    x_pos = np.arange(len(chip_ids))
    
    axes[0, 0].bar(x_pos - 0.2, xppm_values, 0.4, label='Xppm', alpha=0.7)
    axes[0, 0].bar(x_pos + 0.2, yppm_values, 0.4, label='Yppm', alpha=0.7)
    axes[0, 0].set_xlabel('芯片ID')
    axes[0, 0].set_ylabel('PPM值')
    axes[0, 0].set_title('各芯片PPM值对比')
    axes[0, 0].set_xticks(x_pos)
    axes[0, 0].set_xticklabels([id.split('L')[1] for id in chip_ids], rotation=45)
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 误差分析
    rms_errors = [r['rms_error'] for r in results_summary]
    max_errors = [r['max_error'] for r in results_summary]
    
    axes[0, 1].bar(x_pos - 0.2, rms_errors, 0.4, label='RMS误差', alpha=0.7)
    axes[0, 1].bar(x_pos + 0.2, max_errors, 0.4, label='最大误差', alpha=0.7)
    axes[0, 1].set_xlabel('芯片ID')
    axes[0, 1].set_ylabel('误差')
    axes[0, 1].set_title('变换误差分析')
    axes[0, 1].set_xticks(x_pos)
    axes[0, 1].set_xticklabels([id.split('L')[1] for id in chip_ids], rotation=45)
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 坐标散点图 (第一个芯片作为示例)
    if len(data['BM']) > 0:
        coords_bm = data['BM'][0]
        coords_ps = data['PS'][0]
        
        axes[1, 0].scatter(coords_bm[:, 0], coords_bm[:, 1], alpha=0.6, label='BM (缩放前)', s=30)
        axes[1, 0].scatter(coords_ps[:, 0], coords_ps[:, 1], alpha=0.6, label='PS (缩放后)', s=30)
        axes[1, 0].set_xlabel('X坐标')
        axes[1, 0].set_ylabel('Y坐标')
        axes[1, 0].set_title(f'坐标对比 - {chip_ids[0]}')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].axis('equal')
    
    # PPM值散点图
    axes[1, 1].scatter(xppm_values, yppm_values, s=100, alpha=0.7)
    for i, chip_id in enumerate(chip_ids):
        axes[1, 1].annotate(chip_id.split('L')[1], 
                           (xppm_values[i], yppm_values[i]), 
                           xytext=(5, 5), textcoords='offset points')
    axes[1, 1].set_xlabel('Xppm (ppm)')
    axes[1, 1].set_ylabel('Yppm (ppm)')
    axes[1, 1].set_title('Xppm vs Yppm 分布')
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].axhline(y=0, color='k', linestyle='--', alpha=0.3)
    axes[1, 1].axvline(x=0, color='k', linestyle='--', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('BM_to_PS_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("可视化图表已保存为: BM_to_PS_analysis.png")

if __name__ == "__main__":
    main()
