import pandas as pd
import numpy as np

try:
    import spotfire
    platform = 'spotfire'
except:
    platform = 'ide'

if platform == 'ide':
    dv_data = pd.read_excel('data/DV_DATA.xlsx')
    tp_data = pd.read_excel('data/All TP Data.xlsx')

# 1.声明 dv_data 和 tp_data 已存在


# 2.dv去重
# 分组依据为'PRODUCT_ID', 'STEP_ID', 'GLASS_ID', 'PARAM_NAME'
# 保留GLASS_START_TIME最迟的记录


# 3.dv处理/转置
# 以PRODUCT_ID、GLASS_ID作为索引，{STEP_ID}_{PARAM_NAME}、GLASS_START_TIME作为列
# PARAM_NAME值为DISPENSE_SPEED时，值取PARAM_VALUE列；为PR_ID时，值取STR_VALUE

