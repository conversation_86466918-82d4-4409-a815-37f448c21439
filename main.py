import pandas as pd
import numpy as np

try:
    import spotfire
    platform = 'spotfire'
except:
    platform = 'ide'

if platform == 'ide':
    dv_data = pd.read_excel('data/DV_DATA.xlsx')
    tp_data = pd.read_excel('data/All TP Data.xlsx')

# DV去重
# 分组依据为'PRODUCT_ID', 'STEP_ID', 'GLASS_ID', 'PARAM_NAME'
# 保留GLASS_START_TIME最迟的记录
dv_data_dedup = dv_data.sort_values('GLASS_START_TIME').groupby(
    ['PRODUCT_ID', 'STEP_ID', 'GLASS_ID', 'PARAM_NAME']
).last().reset_index()


# DV处理/转置
# 以PRODUCT_ID、GLASS_ID作为索引，{STEP_ID}_{PARAM_NAME}、{STEP_ID}_{GLASS_START_TIME}作为列
# PARAM_NAME值为DISPENSE_SPEED时，值取PARAM_VALUE列；为PR_ID时，值取STR_VALUE

# 创建值列：根据PARAM_NAME选择对应的值
dv_data_dedup['VALUE'] = dv_data_dedup.apply(
    lambda row: row['PARAM_VALUE'] if row['PARAM_NAME'] == 'DISPENSE_SPEED' else row['STR_VALUE'],
    axis=1
)

# 创建新的列名：{STEP_ID}_{PARAM_NAME}
dv_data_dedup['COLUMN_NAME'] = dv_data_dedup['STEP_ID'].astype(str) + '_' + dv_data_dedup['PARAM_NAME']

# 准备数据透视的数据 - 包含参数值
pivot_data = dv_data_dedup[['PRODUCT_ID', 'GLASS_ID', 'COLUMN_NAME', 'VALUE']].copy()

# 准备时间数据 - 创建{STEP_ID}_{GLASS_START_TIME}格式
time_data = dv_data_dedup[['PRODUCT_ID', 'GLASS_ID', 'STEP_ID', 'GLASS_START_TIME']].copy()
time_data['TIME_COLUMN_NAME'] = time_data['STEP_ID'].astype(str) + '_GLASS_START_TIME'
time_data = time_data[['PRODUCT_ID', 'GLASS_ID', 'TIME_COLUMN_NAME', 'GLASS_START_TIME']].drop_duplicates()

# 数据透视：参数值
dv_pivot = pivot_data.pivot_table(
    index=['PRODUCT_ID', 'GLASS_ID'],
    columns='COLUMN_NAME',
    values='VALUE',
    aggfunc='first'  # 如果有重复，取第一个值
).reset_index()

# 数据透视：时间值
time_pivot = time_data.pivot_table(
    index=['PRODUCT_ID', 'GLASS_ID'],
    columns='TIME_COLUMN_NAME',
    values='GLASS_START_TIME',
    aggfunc='first'  # 如果有重复，取第一个值
).reset_index()

# 合并参数数据和时间数据
dv_final = dv_pivot.merge(time_pivot, on=['PRODUCT_ID', 'GLASS_ID'], how='left')

# 输出DV处理结果
if platform == 'ide':
    print("DV数据处理完成！")
    print(f"原始数据形状: {dv_data.shape}")
    print(f"去重后数据形状: {dv_data_dedup.shape}")
    print(f"转置后数据形状: {dv_final.shape}")
    print(f"转置后列名: {dv_final.columns.tolist()}")
    print("\n转置后数据预览:")
    print(dv_final.head())





if platform == 'ide':
    dv_final.to_excel('data/DV_DATA_processed.xlsx', index=False)
